<?php
return [
    [
        'text' => 'Data Master',
        'icon' => 'ti ti-database', // Database icon for data master
        'route' => '#', // Route Name | # for non route
        'roles' => ['Admin', 'Tim ADS'], // Roles Menu
        'submenu' => [
            // [
            //     'text' => 'Kategori Produk',
            //     'route' => 'data-master.kategori-produk.index', // / Route Name | # for non route
            //     'roles' => ['Admin'], // Roles Menu
            // ],
            [
                'text' => 'Produk',
                'route' => 'data-master.product.index', // Route Name for product
                'roles' => ['Tim ADS'], // Roles Menu
            ],
            // [
            //     'text' => 'Master Perhitungan',
            //     'route' => 'data-master.perhitungan.index', // Nama route untuk read email
            //     'roles' => ['Admin'], // Roles Menu
            // ],
            [
                'text' => 'Pegawai',
                'route' => 'data-master.pegawai.index', // Nama route untuk read email
                'roles' => ['Admin'], // Roles Menu
            ],
            // [
            //     'text' => 'Supplier',
            //     'route' => '#', // Nama route untuk read email
            //     'roles' => ['Admin'], // Roles Menu
            // ],
            [
                'text' => 'Biaya Iklan',
                'route' => 'data-master.biaya-iklan.index', // Nama route untuk read email
                'roles' => ['Admin'], // Roles Menu
            ],
            [
                'text' => 'Earning Potential',
                'route' => 'data-master.earning-potential.index', // Nama route untuk read email
                'roles' => ['Admin'], // Roles Menu
            ]
        ]
    ],
    [
        'text' => 'Order',
        'icon' => 'ti ti-shopping-cart', // Shopping cart icon for order
        'route' => 'order.index', // Route Name | # for non route
        'roles' => ['Admin', 'CS', 'Gudang', 'CS Manajer'] // Role
    ],
    [
        'text' => 'Landing Page',
        'icon' => 'ti ti-app-window', // Shopping cart icon for order
        'route' => 'landingPage.index', // Route Name | # for non route
        'roles' => ['Tim ADS'] // Role
    ],
    [
        'text' => 'Stok',
        'icon' => 'ti ti-box', // Box icon for stock
        'route' => 'stok.index', // Route Name | # for non route
        'roles' => ['Admin'] // Role
    ],
    [
        'text' => 'Monitoring Pengiriman',
        'icon' => 'ti ti-truck-delivery', // Truck delivery icon for shipment
        'route' => 'monitoring.pengiriman', // Route Name | # for non route
        'roles' => ['Admin', 'Shipment', 'CS'] // Role
    ],
    [
        'text' => 'Retur',
        'icon' => 'ti ti-restore',
        'route' => 'gudang.retur.index', // Route Name | # for non route
        'roles' => ['Admin', 'Gudang'] // Role
    ],
    [
        'text' => 'Stok Retur',
        'icon' => 'ti ti-brand-stocktwits',
        'route' => 'gudang.stok-retur.index', // Route Name | # for non route
        'roles' => ['Gudang'] // Role
    ],
    [
        'text' => 'Materi Iklan',
        'icon' => 'ti ti-ad', // Ad icon for advertising materials
        'route' => 'ads.materi-iklan.index', // Route Name | # for non route
        'roles' => ['Tim ADS'] // Role
    ],
    [
        'text' => 'Earning Potential',
        'icon' => 'ti ti-cash', // Cash icon for earning potential
        'route' => 'data-master.earning-potential.index', // Nama route untuk read email
        // 'route' => 'ads.earning-potential.index', // Route Name | # for non route
        'roles' => ['Tim ADS'] // Role
    ],
    // [
    //     'text' => 'Monitoring Pengiriman',
    //     'icon' => 'ri-file-list-3-line',
    //     'route' => '#', // Route Name | # for non route
    //     'roles' => ['Shipment'] // Role
    // ],

    [
        'text' => 'Biaya Iklan',
        'icon' => 'ti ti-receipt', // Receipt icon for ad costs
        'route' => 'ads.biaya-iklan.index', // Route Name | # for non route
        'roles' => ['Tim ADS'] // Role
    ],
    [
        'text' => 'Payment ADV',
        'icon' => 'ti ti-receipt', // Receipt icon for ad costs
        'route' => 'payment-adv.index', // Route Name | # for non route
        'roles' => ['Admin', 'Tim ADS'] // Role
    ],
    [
        'text' => 'Ratio',
        'icon' => 'ti ti-square-percentage', // Percentage icon for ratio
        'route' => '#', // Route Name | # for non route
        'roles' => ['CS', 'Tim ADS'], // Roles Menu
        'submenu' => [
            [
                'text' => 'Ratio Close Per Order',
                'route' => 'ratio.ratio-per-order.index', // Nama route untuk read email
                'roles' => ['CS'], // Roles Menu
            ],
            [
                'text' => 'Ratio Order Per Produk   ',
                'route' => 'ratio.ratio-per-produk.index', // Nama route untuk read email
                'roles' => ['Tim ADS'], // Roles Menu
            ],
            [
                'text' => 'Ratio Close Order Per ADV',
                'route' => 'ratio.ratio-per-adv.index', // Nama route untuk read email
                'roles' => ['CS'], // Roles Menu
            ],
        ]
    ],
    [
        'text' => 'Rekap',
        'icon' => 'ti ti-file-analytics', // File analytics icon for recap
        'route' => '#', // Route Name | # for non route
        'roles' => ['CS'], // Roles Menu
        'submenu' => [
            [
                'text' => 'Metode Pembayaran',
                'route' => 'rekap.metode-pembayaran.index', // Nama route untuk read email
                'roles' => ['CS'], // Roles Menu
            ]
        ]
    ],
    [
        'text' => 'API Key',
        'icon' => 'ti ti-key', // Key icon for API configuration
        'route' => 'api-keys.index', // Route Name | # for non route
        'roles' => ['Admin'] // Role
    ],
    [
        'text' => 'Laporan',
        'icon' => 'ti ti-report', // Report icon for reports
        'route' => '#', // Route Name | # for non route
        'roles' => ['Admin', 'CS', 'Gudang', 'Tim ADS', 'Shipment', 'CS Manajer'], // Roles Menu
        'submenu' => [
            [
                'text' => 'Laporan Payment CS',
                'route' => 'laporan.payment-cs.index', // / Route Name | # for non route
                'roles' => ['Admin', 'CS', 'CS Manajer'], // Roles Menu
            ],
            [
                'text' => 'Laporan Payment Adv',
                'route' => 'laporan.payment-adv.index', // Nama route untuk read email
                'roles' => ['Admin', 'Tim ADS'], // Roles Menu
            ],
            // [
            //     'text' => 'Laporan Pengiriman',
            //     'route' => '#', // Nama route untuk read email
            //     'roles' => ['Admin', 'CS'], // Roles Menu
            // ],
            [
                'text' => 'Laporan Retur',
                'route' => 'laporan.retur.index', // Nama route untuk read email
                'roles' => ['CS', 'CS Manajer'], // Roles Menu
            ],
            [
                'text' => 'Laporan Stok',
                'route' => 'laporan.stok.index', // Nama route untuk read email
                'roles' => ['Gudang', 'Shipment'], // Roles Menu
            ],
            [
                'text' => 'Laporan Fee Gudang COD',
                'route' => 'laporan.fee-gudang-cod.index', // Nama route untuk read email
                'roles' => ['Gudang'], // Roles Menu
            ],
            [
                'text' => 'Laporan Fee Gudang TF',
                'route' => 'laporan.fee-gudang-tf.index', // Nama route untuk read email
                'roles' => ['Gudang'], // Roles Menu
            ],
            [
                'text' => 'Laporan Fee Gudang Retur',
                'route' => 'laporan.fee-gudang-retur.index', // Nama route untuk read email
                'roles' => ['Gudang'], // Roles Menu
            ],
            [
                'text' => 'Laporan List Kulak',
                'route' => 'laporan.list-kulak.index', // Nama route untuk read email
                'roles' => ['Gudang'], // Roles Menu
            ],
            [
                'text' => 'Laporan Barang Rusak',
                'route' => 'laporan.barang-rusak.index', // Nama route untuk read email
                'roles' => ['Gudang', 'Shipment'], // Roles Menu
            ],
            [
                'text' => 'Laporan Performa Iklan',
                'route' => 'laporan.performa-iklan.index', // Nama route untuk read email
                'roles' => ['Tim ADS'], // Roles Menu
            ],
            [
                'text' => 'Laporan Cashback',
                'route' => 'laporan.cashback.index', // Nama route untuk read email
                'roles' => ['Shipment'], // Roles Menu
            ],
            [
                'text' => 'Laporan Pengiriman',
                'route' => 'laporan.pengiriman.index', // Nama route untuk read email
                'roles' => ['CS', 'Shipment'], // Roles Menu
            ],
        ]
    ]
];
